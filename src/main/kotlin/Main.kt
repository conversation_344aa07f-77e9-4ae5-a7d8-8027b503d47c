package org.example

import com.fasterxml.jackson.databind.SerializationFeature
import io.ktor.serialization.jackson.jackson
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.engine.embeddedServer
import io.ktor.server.netty.Netty
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.routing
import org.example.database.HikariMariadbPool
import org.example.model.crudRoutes
import org.example.table.CommuteVehicleHistoryLocationService

fun main() {
    embeddedServer(Netty, port = 8004, module = Application::module).start(wait = true)
}
//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
fun Application.module() {
    // 初始化数据库
    HikariMariadbPool

    // 创建服务实例
    val locationService = CommuteVehicleHistoryLocationService()

    install(ContentNegotiation) {
        jackson {
            enable(SerializationFeature.INDENT_OUTPUT)
        }
    }

    routing {
        crudRoutes(
            path = "locations",
            service = locationService,
            idParser = { it } // String ID 不需要转换
        )
    }
}