package org.example.model

import org.example.database.HikariMariadbPool
import org.example.table.CommuteVehicleHistoryLocationTable
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import java.time.LocalDate

// 分页结果数据类
data class PaginatedResult<T>(
    val data: List<T>,
    val totalCount: Long,
    val currentPage: Int,
    val totalPages: Int,
    val pageSize: Int
)

// 为Query添加分页扩展函数
fun <T> Query.paginate(
    page: Int = 1,
    pageSize: Int = 10,
    transform: (ResultRow) -> T
): PaginatedResult<T> {
    val totalCount = this.count()
    val totalPages = (totalCount + pageSize - 1) / pageSize
    val adjustedPage = page.coerceIn(1, totalPages.toInt())
    val offset = (adjustedPage - 1) * pageSize

    val data = this.limit(pageSize, offset.toLong()).map(transform)

    return PaginatedResult(
        data = data,
        totalCount = totalCount,
        currentPage = adjustedPage,
        totalPages = totalPages.toInt(),
        pageSize = pageSize
    )
}

// 使用示例
fun queryWithPaginationExtension(date: LocalDate, page: Int, pageSize: Int) {
    val monthlyTable = CommuteVehicleHistoryLocationTable

    transaction {
        val result = monthlyTable.selectAll().orderBy(monthlyTable.sysCreated, SortOrder.DESC).paginate(page, pageSize) { row ->
            row[monthlyTable.sysCreated]
        }

        println("Page ${result.currentPage} of ${result.totalPages}")
        println("Results: ${result.data}")
        println("Total items: ${result.totalCount}")
    }
}

fun main() {
    HikariMariadbPool

    queryWithPaginationExtension(LocalDate.now(), 1, 2)
}