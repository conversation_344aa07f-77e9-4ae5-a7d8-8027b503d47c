package org.example.table

import org.example.model.BaseEntity
import org.jetbrains.exposed.sql.Table

/**
 * -- datacenter.tbl_aisposition definition
 *
 * CREATE TABLE `tbl_aisposition` (
 *   `id` varchar(36) NOT NULL,
 *   `reportNumber` int(2) NOT NULL,
 *   `shipId` varchar(36) DEFAULT '',
 *   `mmsi` varchar(100) DEFAULT NULL,
 *   `navigationStatusCode` varchar(10) DEFAULT NULL,
 *   `rateOfTurn` int(5) DEFAULT NULL,
 *   `speedOverGround` float DEFAULT NULL,
 *   `positionAccuracy` int(1) DEFAULT '0',
 *   `longitude` decimal(9,6) NOT NULL,
 *   `latitude` decimal(8,6) NOT NULL,
 *   `courseOverGround` float DEFAULT NULL,
 *   `heading` int(5) DEFAULT NULL,
 *   `second` int(5) DEFAULT NULL,
 *   `specialManeuverIndicator` int(1) DEFAULT '0',
 *   `raimFlag` int(1) DEFAULT '0',
 *   `reportTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *   `rawData` varchar(500) DEFAULT NULL,
 *   `sysCreated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *   `sysUpdated` varchar(100) DEFAULT '',
 *   `sysDeleted` tinyint(1) DEFAULT NULL,
 *   `fromType` varchar(2) DEFAULT NULL,
 *   PRIMARY KEY (`id`),
 *   KEY `IDX_tbl_aisposition_mmsi` (`mmsi`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
 */
object AisPositionTable : Table("tbl_aisposition") {
    val id = varchar("id", 36)

}