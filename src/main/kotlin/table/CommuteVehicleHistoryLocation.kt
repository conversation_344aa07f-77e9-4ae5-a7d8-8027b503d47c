package org.example.table

import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

data class CommuteVehicleHistoryLocation(
    val id: String,
    val carId: String,
    val sysCreated: String
)

object CommuteVehicleHistoryLocationTable : Table("tbl_commutevehiclehistorylocation") {
    val id = varchar("id", 32)
    val carId = varchar("carId", 32)
    val sysCreated = datetime("sysCreated")
    override val primaryKey = PrimaryKey(id)
}

fun ResultRow.toCommuteVehicleHistoryLocation(): CommuteVehicleHistoryLocation = CommuteVehicleHistoryLocation(
    this[CommuteVehicleHistoryLocationTable.id],
    this[CommuteVehicleHistoryLocationTable.carId],
    this[CommuteVehicleHistoryLocationTable.sysCreated].toString()
)