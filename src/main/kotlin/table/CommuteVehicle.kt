package org.example.table

import org.example.model.BaseEntity
import org.example.model.BaseTableService
import org.example.model.InsertPair
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

object CommuteVehicleTable : Table("tbl_commutevehicle") {
    val id = varchar("id", 32)
    val carName = varchar("carName", 32)
    val sysCreated = datetime("sysCreated")
    override val primaryKey = PrimaryKey(id)
}

class CommuteVehicle: BaseEntity(CommuteVehicleTable) {
    var carName: String = ""
    var sysCreated: String = ""
}

fun ResultRow.toCommuteVehicle() = CommuteVehicle().apply {
    id = this@toCommuteVehicle[CommuteVehicleTable.id]
    carName = this@toCommuteVehicle[CommuteVehicleTable.carName]
    sysCreated = this@toCommuteVehicle[CommuteVehicleTable.sysCreated].toString()
}

class CommuteVehicleHistoryLocationService : BaseTableService<CommuteVehicle, String, CommuteVehicleTable>(CommuteVehicleTable) {
    override val idColumn: Column<String> = CommuteVehicleHistoryLocationTable.id

    override fun ResultRow.toEntity() = this.toCommuteVehicle()
}
