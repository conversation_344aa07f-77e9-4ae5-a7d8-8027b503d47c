package org.example.database

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import org.example.config.AppConfig
import org.jetbrains.exposed.sql.Database
import java.sql.Connection

object HikariMariadbPool {
    private val hikariDataSource: HikariDataSource

    init {
        val config = HikariConfig().apply {
            jdbcUrl = AppConfig.MARIADB_URL
            driverClassName = "org.mariadb.jdbc.Driver"
            username = "root"
            password = "SHENLAN@2016"
            maximumPoolSize = 10
            isAutoCommit = false
            transactionIsolation = "TRANSACTION_REPEATABLE_READ"
            validate()
        }
        hikariDataSource = HikariDataSource(config)
        Database.Companion.connect(hikariDataSource)
    }

    fun getConnection(): Connection {
        return hikariDataSource.connection
    }
}